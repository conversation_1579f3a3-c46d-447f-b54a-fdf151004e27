import CONFIG from "../config.js";
import authService from "./auth-service.js";

class ProfilePictureService {
  /**
   * Upload a profile picture to the API
   * @param {File} file - The image file to upload
   * @returns {Promise<Object>} - Updated user data with new profile picture URL
   */
  async uploadProfilePicture(file) {
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error("Authentication required");
      }

      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append("profilePicture", file);

      const response = await fetch(
        `${CONFIG.BASE_URL}${CONFIG.API_ENDPOINTS.ACCOUNT.UPLOAD_PROFILE_PICTURE}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to upload profile picture");
      }

      const userData = await response.json();
      
      // Update stored user data
      authService.saveAuthData({ user: userData });
      
      return userData;
    } catch (error) {
      console.error("Profile picture upload error:", error);
      throw error;
    }
  }

  /**
   * Delete the current user's profile picture
   * @returns {Promise<Object>} - Updated user data without profile picture
   */
  async deleteProfilePicture() {
    try {
      const token = authService.getToken();
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(
        `${CONFIG.BASE_URL}${CONFIG.API_ENDPOINTS.ACCOUNT.DELETE_PROFILE_PICTURE}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete profile picture");
      }

      const userData = await response.json();
      
      // Update stored user data
      authService.saveAuthData({ user: userData });
      
      return userData;
    } catch (error) {
      console.error("Profile picture delete error:", error);
      throw error;
    }
  }

  /**
   * Get the full URL for a profile picture
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @returns {string|null} - Full URL to the profile picture or null if none
   */
  getProfilePictureUrl(profilePictureUrl) {
    if (!profilePictureUrl) {
      return null;
    }

    // If it's already a full URL, return as is
    if (profilePictureUrl.startsWith("http://") || profilePictureUrl.startsWith("https://")) {
      return profilePictureUrl;
    }

    // Remove any leading /api/account/profile-picture/ to prevent duplication
    const cleanImagePath = profilePictureUrl.replace(/^\/api\/account\/profile-picture\//, '');
    
    // Construct full URL using the API schema
    return `${CONFIG.BASE_URL}/api/account/profile-picture/${cleanImagePath}`;
  }

  /**
   * Create a fallback avatar element with user's initial
   * @param {string} username - User's username
   * @param {string} size - Size class ('small', 'medium', 'large')
   * @returns {string} - HTML string for fallback avatar
   */
  createFallbackAvatar(username, size = 'medium') {
    const initial = (username || 'U').charAt(0).toUpperCase();
    const sizeClasses = {
      small: 'width: 40px; height: 40px; font-size: 16px;',
      medium: 'width: 80px; height: 80px; font-size: 24px;',
      large: 'width: 120px; height: 120px; font-size: 36px;'
    };
    
    return `
      <div class="author-avatar fallback-avatar ${size}" style="
        ${sizeClasses[size]}
        border-radius: 50%; 
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex; align-items: center; justify-content: center;
        color: white; font-weight: bold;
      ">
        ${initial}
      </div>
    `;
  }

  /**
   * Update an image element with profile picture or fallback
   * @param {HTMLImageElement} imgElement - The image element to update
   * @param {string} profilePictureUrl - The profile picture URL from user data
   * @param {string} username - User's username for fallback
   */
  updateImageElement(imgElement, profilePictureUrl, username) {
    if (!imgElement) return;

    const fullUrl = this.getProfilePictureUrl(profilePictureUrl);
    
    if (fullUrl) {
      imgElement.src = fullUrl;
      imgElement.alt = `${username || 'User'} Profile Picture`;
      imgElement.style.display = 'block';
      
      // Set up error handling for failed image loads
      imgElement.onerror = () => {
        console.warn("Failed to load profile picture, using fallback");
        imgElement.style.display = 'none';
        
        // Create and insert fallback avatar
        const fallbackHtml = this.createFallbackAvatar(username);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = fallbackHtml;
        const fallbackElement = tempDiv.firstElementChild;
        
        imgElement.parentNode.insertBefore(fallbackElement, imgElement);
      };
    } else {
      // No profile picture available, hide image and show fallback
      imgElement.style.display = 'none';
      
      // Check if fallback already exists
      const existingFallback = imgElement.parentNode.querySelector('.fallback-avatar');
      if (!existingFallback) {
        const fallbackHtml = this.createFallbackAvatar(username);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = fallbackHtml;
        const fallbackElement = tempDiv.firstElementChild;
        
        imgElement.parentNode.insertBefore(fallbackElement, imgElement);
      }
    }
  }
}

export default new ProfilePictureService();
